name: learning2
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.7.2 <4.0.0'
  flutter: ">=3.29.3"

dependencies:
  flutter:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  flutter_animate: ^4.5.0

  # State management and routing
  provider: ^6.1.1
  get: ^4.7.2
  flutter_hooks: ^0.20.5

  # Firebase & Backend
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.14.0
  firebase_messaging: ^14.7.0

  # Form helpers and utils
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3
  dart_jsonwebtoken: ^3.0.0
  uuid: ^4.3.3
  intl: ^0.19.0

  # Input & Files
  dropdown_search: ^5.0.6
  image_picker: ^1.0.7
  path_provider: ^2.1.2
  open_filex: ^4.3.2
  excel: ^4.0.2
  csv: ^5.1.1
  pdf: ^3.10.7

  # Charts & Graphs
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^24.1.47

  # Connectivity
  geolocator: ^10.1.0
  connectivity_plus: ^6.0.3
  device_info_plus: ^10.1.2
  permission_handler: ^11.0.0

  # Local storage
  sqflite: ^2.3.0
  path: ^1.8.3

  # Network
  http: ^1.2.0
  dio: ^5.4.0
  web_socket_channel: ^3.0.3

  # Scanner
  mobile_scanner: ^3.5.6

  # Feedback
  fluttertoast: ^8.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

  # Optional scanner fork
  qr_code_scanner:
    git:
      url: https://github.com/asmrtfm/qr_code_scanner
      ref: patch-1
    version: ^1.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/



# Notes:
# - Using Google Fonts for Urbanist font family (google_fonts dependency included).
