# Flutter High Refresh Rate Performance Optimization Guide

## Overview

This guide documents the comprehensive performance optimizations implemented in your SPARSH Flutter application to achieve high refresh rates (90Hz, 120Hz, 144Hz+) and smooth animations.

## Implemented Optimizations

### 1. High Refresh Rate Support

#### Android Configuration
- **MainActivity.kt**: Added native Android code to enable high refresh rate displays
- **AndroidManifest.xml**: Configured hardware acceleration and display preferences
- **Method Channels**: Created communication bridge between Flutter and Android for refresh rate control

#### Flutter Configuration
- **DisplayService**: Service to detect and enable high refresh rate
- **Performance Utils**: Utilities for optimizing widgets based on refresh rate
- **Main.dart**: Configured app-wide performance settings

### 2. Widget Performance Optimizations

#### RepaintBoundary Usage
- Added `RepaintBoundary` widgets around expensive UI components
- Isolated animations and complex widgets to prevent unnecessary repaints
- Wrapped bottom navigation, QR scanner, and banner components

#### Optimized Animations
- Reduced animation complexity for better performance
- Adjusted animation durations based on device refresh rate
- Optimized shadow blur radius and gradient complexity
- Implemented performance-aware animation curves

#### Scroll Performance
- Custom scroll physics for high refresh rate displays
- Optimized ListView and GridView implementations
- Disabled unnecessary scrollbars for better performance

### 3. Performance Monitoring

#### Real-time Monitoring
- **PerformanceMonitor Widget**: Tracks FPS, frame times, and dropped frames
- **Performance Metrics**: Provides detailed performance analytics
- **Logging**: Optional performance logging for debugging

#### Adaptive Optimizations
- Automatic performance adjustments based on device capabilities
- Refresh rate category detection (Standard, High, Very High, Ultra)
- Dynamic animation duration optimization

## Usage Instructions

### 1. Enable Performance Monitoring (Debug Mode)

To see real-time performance metrics during development:

```dart
// In main.dart, change showOverlay to true
home: const PerformanceMonitor(
  showOverlay: true,  // Enable performance overlay
  enableLogging: true,
  child: HomeScreen(),
),
```

### 2. Check Device Refresh Rate

```dart
// Get current refresh rate
final refreshRate = await DisplayService.instance.getRefreshRate();
print('Device refresh rate: ${refreshRate}Hz');

// Check if high refresh rate is supported
final supportsHighRefreshRate = await DisplayService.instance.supportsHighRefreshRate();
print('Supports high refresh rate: $supportsHighRefreshRate');
```

### 3. Use Performance-Optimized Widgets

```dart
// Use optimized containers
PerformanceUtils.createOptimizedAnimatedContainer(
  child: YourWidget(),
  duration: DisplayService.instance.getOptimalAnimationDuration(),
);

// Use optimized gradients
decoration: PerformanceUtils.createOptimizedGradient(
  colors: [Colors.blue, Colors.purple],
  boxShadow: PerformanceUtils.createOptimizedShadow(),
);

// Use optimized lists
PerformanceUtils.createOptimizedListView(
  children: yourWidgets,
);
```

## Performance Features

### 1. Automatic Refresh Rate Detection
- Detects device refresh rate on app startup
- Enables highest available refresh rate automatically
- Provides fallback for unsupported devices

### 2. Adaptive Performance Settings
- **Ultra (144Hz+)**: Maximum performance optimizations
- **Very High (120Hz)**: High performance with some optimizations
- **High (90Hz)**: Balanced performance and quality
- **Standard (60Hz)**: Conservative optimizations for older devices

### 3. Frame Rate Monitoring
- Real-time FPS tracking
- Dropped frame detection
- Performance grade calculation (A-F)
- Average frame time analysis

## Performance Recommendations

### For High Refresh Rate Devices (90Hz+)
1. Use RepaintBoundary for complex widgets
2. Optimize animation durations
3. Reduce shadow blur radius
4. Enable hardware acceleration
5. Use const constructors where possible

### For Standard Devices (60Hz)
1. Reduce animation complexity
2. Limit gradient usage
3. Optimize image loading
4. Use simpler shadow effects
5. Implement lazy loading for lists

## Testing Performance

### 1. Enable Performance Overlay
Set `showOverlay: true` in PerformanceMonitor to see:
- Current FPS
- Average FPS
- Frame time
- Dropped frames
- Performance grade

### 2. Monitor Console Logs
Enable logging to see:
- Dropped frame notifications
- Performance warnings
- Refresh rate information

### 3. Performance Metrics
Access detailed metrics programmatically:
```dart
final metrics = performanceMonitor.getMetrics();
print('Performance Grade: ${metrics.performanceGrade}');
print('Drop Rate: ${(metrics.dropRate * 100).toStringAsFixed(1)}%');
```

## Troubleshooting

### Low FPS Issues
1. Check if RepaintBoundary is properly used
2. Reduce animation complexity
3. Optimize heavy widgets
4. Check for memory leaks
5. Profile using Flutter DevTools

### High Refresh Rate Not Working
1. Verify device supports high refresh rate
2. Check Android permissions
3. Ensure hardware acceleration is enabled
4. Test on physical device (not emulator)

### Performance Degradation
1. Monitor performance metrics
2. Check for unnecessary rebuilds
3. Optimize setState usage
4. Use const constructors
5. Profile widget rebuilds

## Build Configuration

### Release Build Optimizations
```bash
# Build with performance optimizations
flutter build apk --release --target-platform android-arm64
flutter build appbundle --release
```

### Profile Build for Testing
```bash
# Build profile version for performance testing
flutter build apk --profile
flutter run --profile
```

## Future Enhancements

1. **iOS High Refresh Rate Support**: Implement ProMotion support for iOS devices
2. **Dynamic Quality Adjustment**: Automatically adjust quality based on performance
3. **Battery Optimization**: Balance performance with battery life
4. **Advanced Caching**: Implement intelligent widget caching
5. **GPU Optimization**: Leverage GPU acceleration for complex animations

## Performance Metrics Goals

- **Target FPS**: Match device refresh rate (60/90/120/144 Hz)
- **Frame Drop Rate**: < 5% for good performance, < 1% for excellent
- **Frame Time**: Consistent timing within refresh rate limits
- **Performance Grade**: Aim for A or B grade consistently

## Support

For performance-related issues or questions:
1. Check performance overlay for real-time metrics
2. Enable logging for detailed information
3. Use Flutter DevTools for advanced profiling
4. Monitor device-specific performance characteristics
