{"buildFiles": ["E:\\FlutterDev\\sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\sparsh1\\New-Sparsh-minimal-branch\\android\\app\\.cxx\\Debug\\5j2d5v2o\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\sparsh1\\New-Sparsh-minimal-branch\\android\\app\\.cxx\\Debug\\5j2d5v2o\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}