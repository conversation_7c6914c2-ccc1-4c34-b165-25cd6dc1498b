{"buildFiles": ["E:\\FlutterDev\\sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\SPARSH8\\android\\app\\.cxx\\Debug\\4r4r3v43\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\SPARSH8\\android\\app\\.cxx\\Debug\\4r4r3v43\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}