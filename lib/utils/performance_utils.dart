import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

/// Performance utilities for optimizing Flutter app refresh rate and rendering
class PerformanceUtils {
  
  /// Enable high refresh rate display support
  static Future<void> enableHighRefreshRate() async {
    try {
      // Enable high refresh rate for supported devices
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      
      // Force high refresh rate if available
      SchedulerBinding.instance.addPostFrameCallback((_) {
        SchedulerBinding.instance.scheduleWarmUpFrame();
      });
      
      if (kDebugMode) {
        debugPrint('High refresh rate enabled successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error enabling high refresh rate: $e');
      }
    }
  }
  
  /// Configure optimal system UI for performance
  static Future<void> configureSystemUI() async {
    await SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));
  }
  
  /// Get optimal scroll physics for high refresh rate
  static ScrollPhysics getOptimalScrollPhysics() {
    return const BouncingScrollPhysics(
      parent: AlwaysScrollableScrollPhysics(),
    );
  }
  
  /// Create a RepaintBoundary wrapper for expensive widgets
  static Widget wrapWithRepaintBoundary({
    required Widget child,
    String? debugLabel,
  }) {
    return RepaintBoundary(
      child: child,
    );
  }
  
  /// Create an optimized AnimatedContainer with performance considerations
  static Widget createOptimizedAnimatedContainer({
    required Widget child,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeInOut,
    BoxDecoration? decoration,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
  }) {
    return RepaintBoundary(
      child: AnimatedContainer(
        duration: duration,
        curve: curve,
        decoration: decoration,
        padding: padding,
        margin: margin,
        width: width,
        height: height,
        child: child,
      ),
    );
  }
  
  /// Create performance-optimized gradient decoration
  static BoxDecoration createOptimizedGradient({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<BoxShadow>? boxShadow,
    BorderRadius? borderRadius,
  }) {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: colors,
        begin: begin,
        end: end,
      ),
      boxShadow: boxShadow,
      borderRadius: borderRadius,
    );
  }
  
  /// Create optimized shadow with reduced blur for better performance
  static List<BoxShadow> createOptimizedShadow({
    Color color = Colors.black26,
    double blurRadius = 4.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
  }) {
    return [
      BoxShadow(
        color: color,
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }
  
  /// Monitor frame rate and performance
  static void startPerformanceMonitoring() {
    SchedulerBinding.instance.addTimingsCallback((List<FrameTiming> timings) {
      for (final timing in timings) {
        final frameTime = timing.totalSpan.inMicroseconds / 1000.0;
        if (frameTime > 16.67) { // More than 60 FPS threshold
          print('Frame took ${frameTime.toStringAsFixed(2)}ms (dropped frame)');
        }
      }
    });
  }
  
  /// Get device refresh rate information
  static double getTargetFrameRate() {
    final display = WidgetsBinding.instance.platformDispatcher.displays.first;
    return display.refreshRate;
  }
  
  /// Create a performance-optimized ListView
  static Widget createOptimizedListView({
    required List<Widget> children,
    ScrollController? controller,
    bool shrinkWrap = false,
    EdgeInsetsGeometry? padding,
  }) {
    return RepaintBoundary(
      child: ListView(
        controller: controller,
        shrinkWrap: shrinkWrap,
        padding: padding,
        physics: getOptimalScrollPhysics(),
        children: children,
      ),
    );
  }
  
  /// Create a performance-optimized GridView
  static Widget createOptimizedGridView({
    required List<Widget> children,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    bool shrinkWrap = false,
    EdgeInsetsGeometry? padding,
  }) {
    return RepaintBoundary(
      child: GridView(
        controller: controller,
        shrinkWrap: shrinkWrap,
        padding: padding,
        physics: getOptimalScrollPhysics(),
        gridDelegate: gridDelegate,
        children: children,
      ),
    );
  }
  
  /// Debounce function for reducing unnecessary rebuilds
  static void debounce(
    String key,
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }
  
  static final Map<String, Timer> _debounceTimers = {};
  
  /// Dispose all debounce timers
  static void disposeDebounceTimers() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
  }
}

/// Custom scroll behavior for high refresh rate
class HighRefreshRateScrollBehavior extends MaterialScrollBehavior {
  const HighRefreshRateScrollBehavior();
  
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const BouncingScrollPhysics(
      parent: AlwaysScrollableScrollPhysics(),
    );
  }
  
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    // Disable scrollbars for better performance
    return child;
  }
}

/// Performance-optimized StatefulWidget base class
abstract class PerformanceOptimizedStatefulWidget extends StatefulWidget {
  const PerformanceOptimizedStatefulWidget({super.key});
  
  @override
  PerformanceOptimizedState createState();
}

abstract class PerformanceOptimizedState<T extends PerformanceOptimizedStatefulWidget> 
    extends State<T> with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildOptimized(context);
  }
  
  Widget buildOptimized(BuildContext context);
}
