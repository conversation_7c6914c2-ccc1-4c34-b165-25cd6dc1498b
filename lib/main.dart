import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'package:camera/camera.dart';
import 'dart:ui' as ui;
import 'live_camera_screen.dart';

void main() {
  runApp(MaterialApp(home: CementBagCounterApp(), debugShowCheckedModeBanner: false));
}

class CementBagCounterApp extends StatefulWidget {
  @override
  State<CementBagCounterApp> createState() => _CementBagCounterAppState();
}

class _CementBagCounterAppState extends State<CementBagCounterApp> {
  File? _image;
  int? _count;
  bool _loading = false;
  String? _error;
  Map? _lastResult;
  String? _rawJsonInfo;
  List<Map<String, dynamic>>? _detectionBoxes;
  ui.Image? _imageForPainting;
  String _loadingStatus = "Processing...";

  final ImagePicker _picker = ImagePicker();

  // Roboflow latest workflow endpoint
  final String endpoint =
      'https://serverless.roboflow.com/infer/workflows/demo-sa2bz/detect-count-and-visualize-8';

  final String apiKey = 'Npp49e9rNndYTGP94QGd';

  Future<void> _pickImage(ImageSource source) async {
    final picked = await _picker.pickImage(source: source);
    if (picked != null) {
      // Show image immediately for better UX
      setState(() {
        _image = File(picked.path);
        _loading = true;
      });

      // Clear previous results after showing new image
      setState(() {
        _count = null;
        _error = null;
        _lastResult = null;
        _rawJsonInfo = null;
        _detectionBoxes = null;
        _imageForPainting = null;
      });

      // Start detection process
      await _uploadAndDetect(File(picked.path));
    }
  }

  Future<void> _uploadAndDetect(File imageFile) async {
    setState(() {
      _loading = true;
      _count = null;
      _error = null;
      _lastResult = null;
      _loadingStatus = "Optimizing image...";
    });

    try {
      // Start loading image for painting immediately (parallel processing)
      final imageLoadingFuture = _loadImageForPainting(imageFile);

      // Optimize image size for faster upload (compress if too large)
      final bytes = await _optimizeImageBytes(imageFile);

      setState(() {
        _loadingStatus = "Encoding image...";
      });

      final base64Image = base64Encode(bytes);

      setState(() {
        _loadingStatus = "Detecting cement bags...";
      });

      // Use optimized HTTP client with the latest endpoint
      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          "Accept-Encoding": "gzip, deflate",
          "Connection": "keep-alive",
        },
        body: json.encode({
          "api_key": apiKey,
          "inputs": {
            "image": {"type": "base64", "value": base64Image}
          }
        }),
      ).timeout(Duration(seconds: 15)); // Faster timeout

      if (response.statusCode == 200) {
        setState(() {
          _loadingStatus = "Processing results...";
        });

        final data = json.decode(response.body);
        int count = 0;
        String rawJsonInfo = "";

        // Check for count_objects in raw JSON first (priority)
        List<String> jsonAnalysis = [];

        // Check all possible count_objects locations
        if (data['count_objects'] != null) {
          jsonAnalysis.add("count_objects: ${data['count_objects']}");
          count = _extractCount(data['count_objects']);
        }
        if (data['results'] != null && data['results']['count_objects'] != null) {
          jsonAnalysis.add("results.count_objects: ${data['results']['count_objects']}");
          if (count == 0) count = _extractCount(data['results']['count_objects']);
        }
        if (data['results'] != null && data['results']['image'] != null && data['results']['image']['count_objects'] != null) {
          jsonAnalysis.add("results.image.count_objects: ${data['results']['image']['count_objects']}");
          if (count == 0) count = _extractCount(data['results']['image']['count_objects']);
        }

        // Check for count_objects in outputs array
        if (data['outputs'] is List && (data['outputs'] as List).isNotEmpty) {
          final outputs = data['outputs'] as List;
          for (int i = 0; i < outputs.length; i++) {
            if (outputs[i] is Map && outputs[i]['count_objects'] != null) {
              jsonAnalysis.add("outputs[$i].count_objects: ${outputs[i]['count_objects']}");
              if (count == 0) count = _extractCount(outputs[i]['count_objects']);
            }
          }
        }

        // If no count_objects found, fall back to filtered predictions
        if (count == 0 && data['results'] != null &&
            data['results']['image'] != null &&
            data['results']['image']['predictions'] is List) {

          final predictions = data['results']['image']['predictions'] as List;
          final filteredPredictions = predictions
              .where((d) => d['class'] == 'bags' && (d['confidence'] ?? 0) > 0.5)
              .toList();

          count = filteredPredictions.length;
          jsonAnalysis.add("Filtered 'bags' predictions (confidence > 50%): $count");
        }

        rawJsonInfo = jsonAnalysis.isNotEmpty
            ? "Raw JSON Analysis:\n${jsonAnalysis.join('\n')}\n\nUsing count: $count"
            : "No count fields found in JSON";

        // Extract detection boxes for visualization from the new endpoint
        List<Map<String, dynamic>> detectionBoxes = [];

        if (data['results'] != null &&
            data['results']['image'] != null &&
            data['results']['image']['predictions'] is List) {

          final predictions = data['results']['image']['predictions'] as List;

          for (var prediction in predictions) {
            if (prediction is Map && prediction['class'] == 'bags' && (prediction['confidence'] ?? 0) > 0.5) {
              // Extract bounding box coordinates
              double? x, y, width, height;
              String? className;
              double? confidence;

              // Try different coordinate formats
              if (prediction['x'] != null && prediction['y'] != null &&
                  prediction['width'] != null && prediction['height'] != null) {
                x = (prediction['x'] as num).toDouble();
                y = (prediction['y'] as num).toDouble();
                width = (prediction['width'] as num).toDouble();
                height = (prediction['height'] as num).toDouble();
              } else if (prediction['bbox'] is List && (prediction['bbox'] as List).length >= 4) {
                final bbox = prediction['bbox'] as List;
                x = (bbox[0] as num).toDouble();
                y = (bbox[1] as num).toDouble();
                width = (bbox[2] as num).toDouble();
                height = (bbox[3] as num).toDouble();
              }

              className = prediction['class'] ?? 'bags';
              confidence = (prediction['confidence'] ?? 0.0) as double;

              if (x != null && y != null && width != null && height != null) {
                detectionBoxes.add({
                  'x': x,
                  'y': y,
                  'width': width,
                  'height': height,
                  'class': className,
                  'confidence': confidence,
                });
              }
            }
          }
        }

        // Wait for image loading (started earlier in parallel)
        await imageLoadingFuture;

        setState(() {
          _count = count;
          _lastResult = data;
          _rawJsonInfo = rawJsonInfo;
          _detectionBoxes = detectionBoxes;
          _loading = false;
        });
      } else {
        setState(() {
          _loading = false;
          _error = "API Error: ${response.statusCode}\n${response.body}";
          _rawJsonInfo = null;
        });
      }
    } catch (e) {
      setState(() {
        _loading = false;
        _error = "Error: $e";
      });
    }
  }

  int _extractCount(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    if (value is List) return value.length;
    return 0;
  }

  Future<Uint8List> _optimizeImageBytes(File imageFile) async {
    final bytes = await imageFile.readAsBytes();

    // If image is larger than 1MB, compress it for faster upload
    if (bytes.length > 1024 * 1024) {
      try {
        final codec = await ui.instantiateImageCodec(
          bytes,
          targetWidth: 1024, // Resize to max 1024px width for faster processing
          targetHeight: 1024,
        );
        final frame = await codec.getNextFrame();
        final byteData = await frame.image.toByteData(format: ui.ImageByteFormat.png);
        return byteData!.buffer.asUint8List();
      } catch (e) {
        // If compression fails, use original
        return bytes;
      }
    }
    return bytes;
  }

  Future<void> _loadImageForPainting(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    setState(() {
      _imageForPainting = frame.image;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Cement Bag Counter (Workflow API)'),
      ),
      body: Center(
        child: _loading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                  SizedBox(height: 20),
                  Text(
                    _loadingStatus,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.green[700],
                    ),
                  ),
                  SizedBox(height: 10),
                  Text(
                    "⚡ Optimized for speed",
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _image != null
                        ? Container(
                            height: 300,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: _imageForPainting != null && _detectionBoxes != null && _detectionBoxes!.isNotEmpty
                                  ? AspectRatio(
                                      aspectRatio: _imageForPainting!.width / _imageForPainting!.height,
                                      child: CustomPaint(
                                        painter: DetectionBoxPainter(_imageForPainting!, _detectionBoxes!),
                                        size: Size.infinite,
                                      ),
                                    )
                                  : Image.file(_image!, fit: BoxFit.contain),
                            ),
                          )
                        : Container(
                            height: 300,
                            color: Colors.grey[300],
                            child: Icon(Icons.photo, size: 120, color: Colors.grey[700]),
                          ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.camera),
                          icon: Icon(Icons.camera_alt),
                          label: Text("Camera"),
                        ),
                        const SizedBox(width: 20),
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.gallery),
                          icon: Icon(Icons.photo_library),
                          label: Text("Gallery"),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    _error != null
                        ? Text(_error!,
                            style: TextStyle(color: Colors.red, fontSize: 16))
                        : _count != null
                            ? Column(
                                children: [
                                  Text(
                                    "Detected Cement Bags: $_count",
                                    style: TextStyle(
                                        fontSize: 26,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green[700]),
                                  ),
                                  if (_rawJsonInfo != null) ...[
                                    const SizedBox(height: 15),
                                    Container(
                                      padding: EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.blue[50],
                                        border: Border.all(color: Colors.blue[200]!),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "📊 Raw JSON Object Count",
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.blue[800],
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            _rawJsonInfo!,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.blue[700],
                                              fontFamily: 'monospace',
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              )
                            : Text(
                                "Select an image to count cement bags.",
                                style: TextStyle(fontSize: 16),
                              ),
                    const SizedBox(height: 20),
                    if (_lastResult != null)
                      ExpansionTile(
                        title: Text("Detection Details (Debug)"),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Show response structure analysis
                                Text(
                                  "Response Structure Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                                Text("Keys in response: ${_lastResult!.keys.toList()}"),
                                if (_lastResult!['results'] != null)
                                  Text("Keys in results: ${(_lastResult!['results'] as Map).keys.toList()}"),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null)
                                  Text("Keys in results.image: ${(_lastResult!['results']['image'] as Map).keys.toList()}"),

                                // Show count_objects specifically
                                const SizedBox(height: 10),
                                Text(
                                  "Count Objects Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.blue),
                                ),
                                if (_lastResult!['count_objects'] != null)
                                  Text("count_objects: ${_lastResult!['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                if (_lastResult!['results'] != null && _lastResult!['results']['count_objects'] != null)
                                  Text("results.count_objects: ${_lastResult!['results']['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null && _lastResult!['results']['image']['count_objects'] != null)
                                  Text("results.image.count_objects: ${_lastResult!['results']['image']['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                const SizedBox(height: 10),

                                // Try to find and display predictions from any location
                                Builder(builder: (context) {
                                  List<dynamic>? predictions;
                                  String location = "";

                                  if (_lastResult!['results'] != null &&
                                      _lastResult!['results']['image'] != null &&
                                      _lastResult!['results']['image']['predictions'] is List) {
                                    predictions = _lastResult!['results']['image']['predictions'] as List;
                                    location = "results.image.predictions";
                                  } else if (_lastResult!['predictions'] is List) {
                                    predictions = _lastResult!['predictions'] as List;
                                    location = "predictions";
                                  } else if (_lastResult!['results'] != null && _lastResult!['results']['predictions'] is List) {
                                    predictions = _lastResult!['results']['predictions'] as List;
                                    location = "results.predictions";
                                  } else if (_lastResult!['outputs'] is List) {
                                    predictions = _lastResult!['outputs'] as List;
                                    location = "outputs";
                                  }

                                  if (predictions != null && predictions.isNotEmpty) {
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Found ${predictions.length} predictions in: $location",
                                             style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
                                        const SizedBox(height: 5),
                                        ...predictions.take(10).toList().asMap().entries.map((entry) {
                                          final index = entry.key;
                                          final prediction = entry.value;
                                          final confidence = (prediction['confidence'] ?? prediction['score'] ?? 0.0);
                                          final confidencePercent = (confidence is double && confidence <= 1.0)
                                              ? confidence * 100
                                              : confidence.toDouble();
                                          final className = prediction['class'] ?? prediction['name'] ?? prediction['label'] ?? 'unknown';
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 1.0),
                                            child: Text(
                                              "Detection ${index + 1}: $className (${confidencePercent.toStringAsFixed(1)}%)",
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: confidencePercent > 50 ? Colors.green : Colors.orange,
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                        if (predictions.length > 10)
                                          Text("... and ${predictions.length - 10} more detections"),
                                      ],
                                    );
                                  } else {
                                    return Text("No predictions found in expected locations",
                                               style: TextStyle(color: Colors.red));
                                  }
                                }),

                                const SizedBox(height: 10),
                                ExpansionTile(
                                  title: Text("Raw JSON Response"),
                                  children: [
                                    SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Text(
                                        JsonEncoder.withIndent('  ').convert(_lastResult),
                                        style: TextStyle(fontSize: 10),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
      ),
    );
  }
}

class DetectionBoxPainter extends CustomPainter {
  final ui.Image image;
  final List<Map<String, dynamic>> detectionBoxes;

  DetectionBoxPainter(this.image, this.detectionBoxes);

  @override
  void paint(Canvas canvas, Size size) {
    // Draw the image
    final paint = Paint();
    final src = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final dst = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawImageRect(image, src, dst, paint);

    // Calculate scaling factors
    final scaleX = size.width / image.width;
    final scaleY = size.height / image.height;

    // Draw detection boxes
    final boxPaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    for (var box in detectionBoxes) {
      final x = (box['x'] as double) * scaleX;
      final y = (box['y'] as double) * scaleY;
      final width = (box['width'] as double) * scaleX;
      final height = (box['height'] as double) * scaleY;

      // Draw bounding box
      final rect = Rect.fromLTWH(x, y, width, height);
      canvas.drawRect(rect, boxPaint);

      // Draw label exactly like in the reference image
      final className = box['class'] ?? 'bags';
      final confidence = ((box['confidence'] ?? 0.0) * 100).toStringAsFixed(0);
      final label = '$className $confidence%';

      textPainter.text = TextSpan(
        text: label,
        style: TextStyle(
          color: Colors.black,
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();

      // Draw yellow/green label background exactly like reference
      final labelPadding = 4.0;
      final labelRect = Rect.fromLTWH(
        x,
        y - textPainter.height - labelPadding * 2,
        textPainter.width + labelPadding * 2,
        textPainter.height + labelPadding * 2
      );

      // Use yellow-green color like in reference image
      canvas.drawRect(labelRect, Paint()..color = Color(0xFFCCFF00));

      // Draw label text
      textPainter.paint(canvas, Offset(x + labelPadding, y - textPainter.height - labelPadding));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
