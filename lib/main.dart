import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;

void main() {
  runApp(MaterialApp(home: CementBagCounterApp(), debugShowCheckedModeBanner: false));
}

class CementBagCounterApp extends StatefulWidget {
  @override
  State<CementBagCounterApp> createState() => _CementBagCounterAppState();
}

class _CementBagCounterAppState extends State<CementBagCounterApp> {
  File? _image;
  int? _count;
  bool _loading = false;
  String? _error;
  Map? _lastResult;

  final ImagePicker _picker = ImagePicker();

  // Roboflow new workflow endpoint
  final String endpoint =
      'https://serverless.roboflow.com/infer/workflows/demo-sa2bz/detect-count-and-visualize-7';

  final String apiKey = 'Npp49e9rNndYTGP94QGd';

  Future<void> _pickImage(ImageSource source) async {
    final picked = await _picker.pickImage(source: source);
    if (picked != null) {
      setState(() {
        _image = File(picked.path);
        _count = null;
        _error = null;
        _lastResult = null;
        _loading = true;
      });
      await _uploadAndDetect(File(picked.path));
    }
  }

  Future<void> _uploadAndDetect(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      final body = json.encode({
        "api_key": apiKey,
        "inputs": {
          "image": {"type": "base64", "value": base64Image}
        }
      });

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {"Content-Type": "application/json"},
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        int count = 0;
        String debugInfo = "";

        // First, check for count_objects field
        dynamic countObjects;
        if (data['count_objects'] != null) {
          countObjects = data['count_objects'];
          count = countObjects is int ? countObjects : (countObjects is String ? int.tryParse(countObjects) ?? 0 : 0);
          debugInfo = "Found count_objects: $countObjects (using: $count)";
        } else if (data['results'] != null && data['results']['count_objects'] != null) {
          countObjects = data['results']['count_objects'];
          count = countObjects is int ? countObjects : (countObjects is String ? int.tryParse(countObjects) ?? 0 : 0);
          debugInfo = "Found results.count_objects: $countObjects (using: $count)";
        } else if (data['results'] != null && data['results']['image'] != null && data['results']['image']['count_objects'] != null) {
          countObjects = data['results']['image']['count_objects'];
          count = countObjects is int ? countObjects : (countObjects is String ? int.tryParse(countObjects) ?? 0 : 0);
          debugInfo = "Found results.image.count_objects: $countObjects (using: $count)";
        } else {
          // Fallback to predictions counting
          List<dynamic>? predictions;

          // Check common locations for predictions
          if (data['results'] != null &&
              data['results']['image'] != null &&
              data['results']['image']['predictions'] is List) {
            predictions = data['results']['image']['predictions'] as List;
            debugInfo = "Found predictions in: results.image.predictions";
          } else if (data['predictions'] is List) {
            predictions = data['predictions'] as List;
            debugInfo = "Found predictions in: predictions";
          } else if (data['results'] != null && data['results']['predictions'] is List) {
            predictions = data['results']['predictions'] as List;
            debugInfo = "Found predictions in: results.predictions";
          } else if (data['outputs'] is List) {
            predictions = data['outputs'] as List;
            debugInfo = "Found predictions in: outputs";
          }

          if (predictions != null && predictions.isNotEmpty) {
            // Count all predictions first
            int totalPredictions = predictions.length;

            // Count high confidence predictions
            int highConfidencePredictions = predictions.where((d) => (d['confidence'] ?? 0) > 0.5).length;

            // Count by class if available
            int classPredictions = predictions.where((d) {
              final className = (d['class'] ?? d['name'] ?? '').toString().toLowerCase();
              return className.contains('bag') || className.contains('cement');
            }).length;

            // Use the most appropriate count
            count = highConfidencePredictions > 0 ? highConfidencePredictions : totalPredictions;

            debugInfo += "\nTotal: $totalPredictions, High Conf: $highConfidencePredictions, Class Match: $classPredictions";
          } else {
            debugInfo = "No count_objects or predictions found";
          }
        }

        setState(() {
          _count = count;
          _lastResult = data;
          _loading = false;
          _error = debugInfo; // Show debug info in error field
        });
      } else {
        setState(() {
          _loading = false;
          _error = "API Error: ${response.statusCode}\n${response.body}";
        });
      }
    } catch (e) {
      setState(() {
        _loading = false;
        _error = "Error: $e";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Cement Bag Counter (Workflow API)'),
      ),
      body: Center(
        child: _loading
            ? CircularProgressIndicator()
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _image != null
                        ? Image.file(_image!, height: 250)
                        : Container(
                            height: 250,
                            color: Colors.grey[300],
                            child: Icon(Icons.photo, size: 120, color: Colors.grey[700]),
                          ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.camera),
                          icon: Icon(Icons.camera_alt),
                          label: Text("Camera"),
                        ),
                        const SizedBox(width: 20),
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.gallery),
                          icon: Icon(Icons.photo_library),
                          label: Text("Gallery"),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    _error != null
                        ? Text(_error!,
                            style: TextStyle(color: Colors.red, fontSize: 16))
                        : _count != null
                            ? Text(
                                "Detected Cement Bags: $_count",
                                style: TextStyle(
                                    fontSize: 26,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green[700]),
                              )
                            : Text(
                                "Select an image to count cement bags.",
                                style: TextStyle(fontSize: 16),
                              ),
                    const SizedBox(height: 20),
                    if (_lastResult != null)
                      ExpansionTile(
                        title: Text("Detection Details (Debug)"),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Show response structure analysis
                                Text(
                                  "Response Structure Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                                Text("Keys in response: ${_lastResult!.keys.toList()}"),
                                if (_lastResult!['results'] != null)
                                  Text("Keys in results: ${(_lastResult!['results'] as Map).keys.toList()}"),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null)
                                  Text("Keys in results.image: ${(_lastResult!['results']['image'] as Map).keys.toList()}"),
                                const SizedBox(height: 10),

                                // Try to find and display predictions from any location
                                Builder(builder: (context) {
                                  List<dynamic>? predictions;
                                  String location = "";

                                  if (_lastResult!['results'] != null &&
                                      _lastResult!['results']['image'] != null &&
                                      _lastResult!['results']['image']['predictions'] is List) {
                                    predictions = _lastResult!['results']['image']['predictions'] as List;
                                    location = "results.image.predictions";
                                  } else if (_lastResult!['predictions'] is List) {
                                    predictions = _lastResult!['predictions'] as List;
                                    location = "predictions";
                                  } else if (_lastResult!['results'] != null && _lastResult!['results']['predictions'] is List) {
                                    predictions = _lastResult!['results']['predictions'] as List;
                                    location = "results.predictions";
                                  } else if (_lastResult!['outputs'] is List) {
                                    predictions = _lastResult!['outputs'] as List;
                                    location = "outputs";
                                  }

                                  if (predictions != null && predictions.isNotEmpty) {
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Found ${predictions.length} predictions in: $location",
                                             style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
                                        const SizedBox(height: 5),
                                        ...predictions.take(10).toList().asMap().entries.map((entry) {
                                          final index = entry.key;
                                          final prediction = entry.value;
                                          final confidence = (prediction['confidence'] ?? prediction['score'] ?? 0.0);
                                          final confidencePercent = (confidence is double && confidence <= 1.0)
                                              ? confidence * 100
                                              : confidence.toDouble();
                                          final className = prediction['class'] ?? prediction['name'] ?? prediction['label'] ?? 'unknown';
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 1.0),
                                            child: Text(
                                              "Detection ${index + 1}: $className (${confidencePercent.toStringAsFixed(1)}%)",
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: confidencePercent > 50 ? Colors.green : Colors.orange,
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                        if (predictions.length > 10)
                                          Text("... and ${predictions.length - 10} more detections"),
                                      ],
                                    );
                                  } else {
                                    return Text("No predictions found in expected locations",
                                               style: TextStyle(color: Colors.red));
                                  }
                                }),

                                const SizedBox(height: 10),
                                ExpansionTile(
                                  title: Text("Raw JSON Response"),
                                  children: [
                                    SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Text(
                                        JsonEncoder.withIndent('  ').convert(_lastResult),
                                        style: TextStyle(fontSize: 10),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
      ),
    );
  }
}
