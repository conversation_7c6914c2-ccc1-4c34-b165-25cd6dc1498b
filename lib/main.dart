import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

void main() {
  runApp(const LocationTrackerApp());
}

class LocationTrackerApp extends StatelessWidget {
  const LocationTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Location Tracker',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const LocationTrackerHome(),
    );
  }
}

class LocationTrackerHome extends StatefulWidget {
  const LocationTrackerHome({super.key});

  @override
  State<LocationTrackerHome> createState() => _LocationTrackerHomeState();
}

class _LocationTrackerHomeState extends State<LocationTrackerHome> {
  // API endpoint - configured for local development server
  static const String apiEndpoint = 'http://10.0.2.2:5000/api/latiandlongi/AddLocation';

  // Location tracking state
  Position? _currentPosition;
  String _permissionStatus = 'Unknown';
  String _transmissionStatus = 'Not started';
  DateTime? _lastTransmissionTime;
  Timer? _locationTimer;
  bool _isTracking = false;

  // Error handling
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeLocationTracking();
  }

  @override
  void dispose() {
    _locationTimer?.cancel();
    super.dispose();
  }

  Future<void> _initializeLocationTracking() async {
    try {
      // Check and request permissions
      await _checkLocationPermissions();

      // Start location tracking if permissions are granted
      if (_permissionStatus == 'Granted') {
        await _startLocationTracking();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Initialization error: $e';
      });
    }
  }

  Future<void> _checkLocationPermissions() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _permissionStatus = 'Location services disabled';
        });
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _permissionStatus = 'Permission denied';
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _permissionStatus = 'Permission denied forever';
        });
        return;
      }

      setState(() {
        _permissionStatus = 'Granted';
      });
    } catch (e) {
      setState(() {
        _permissionStatus = 'Error: $e';
      });
    }
  }

  Future<void> _startLocationTracking() async {
    if (_isTracking) return;

    setState(() {
      _isTracking = true;
      _transmissionStatus = 'Starting...';
    });

    // Get initial location
    await _getCurrentLocationAndSend();

    // Set up periodic location updates every 30 seconds
    _locationTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _getCurrentLocationAndSend();
    });
  }

  Future<void> _getCurrentLocationAndSend() async {
    try {
      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 0,
        ),
      );

      setState(() {
        _currentPosition = position;
        _errorMessage = null;
      });

      // Send location to API
      await _sendLocationToAPI(position);

    } catch (e) {
      setState(() {
        _errorMessage = 'Location error: $e';
        _transmissionStatus = 'Failed to get location';
      });
    }
  }

  Future<void> _sendLocationToAPI(Position position) async {
    try {
      // Prepare location data
      Map<String, dynamic> locationData = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'timestamp': DateTime.now().toIso8601String(),
        'altitude': position.altitude,
        'speed': position.speed,
      };

      // Send HTTP POST request
      final response = await http.post(
        Uri.parse(apiEndpoint),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(locationData),
      );

      setState(() {
        _lastTransmissionTime = DateTime.now();
        if (response.statusCode == 200 || response.statusCode == 201) {
          _transmissionStatus = 'Success';
        } else {
          _transmissionStatus = 'Failed (${response.statusCode})';
        }
      });

    } catch (e) {
      setState(() {
        _transmissionStatus = 'Network error: $e';
        _lastTransmissionTime = DateTime.now();
      });
    }
  }

  void _stopLocationTracking() {
    _locationTimer?.cancel();
    setState(() {
      _isTracking = false;
      _transmissionStatus = 'Stopped';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Location Tracker'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permission Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Permission Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _permissionStatus == 'Granted'
                              ? Icons.check_circle
                              : Icons.error,
                          color: _permissionStatus == 'Granted'
                              ? Colors.green
                              : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(_permissionStatus),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Current Location Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Location',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_currentPosition != null) ...[
                      Text('Latitude: ${_currentPosition!.latitude.toStringAsFixed(6)}'),
                      Text('Longitude: ${_currentPosition!.longitude.toStringAsFixed(6)}'),
                      Text('Accuracy: ${_currentPosition!.accuracy.toStringAsFixed(2)}m'),
                      Text('Altitude: ${_currentPosition!.altitude.toStringAsFixed(2)}m'),
                      Text('Speed: ${_currentPosition!.speed.toStringAsFixed(2)} m/s'),
                    ] else ...[
                      const Text('No location data available'),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Transmission Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transmission Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _transmissionStatus.contains('Success')
                              ? Icons.cloud_done
                              : _transmissionStatus.contains('Failed') || _transmissionStatus.contains('error')
                                  ? Icons.cloud_off
                                  : Icons.cloud_queue,
                          color: _transmissionStatus.contains('Success')
                              ? Colors.green
                              : _transmissionStatus.contains('Failed') || _transmissionStatus.contains('error')
                                  ? Colors.red
                                  : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(_transmissionStatus)),
                      ],
                    ),
                    if (_lastTransmissionTime != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Last attempt: ${_lastTransmissionTime!.toLocal().toString().substring(0, 19)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Error Message
            if (_errorMessage != null)
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Error',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Control Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _permissionStatus == 'Granted' && !_isTracking
                        ? _startLocationTracking
                        : null,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Start Tracking'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTracking ? _stopLocationTracking : null,
                    icon: const Icon(Icons.stop),
                    label: const Text('Stop Tracking'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade100,
                      foregroundColor: Colors.red.shade700,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // API Endpoint Info
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'API Configuration',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Endpoint: $apiEndpoint',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      'Interval: 30 seconds',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
