import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;

void main() {
  runApp(MaterialApp(home: CementBagCounterApp(), debugShowCheckedModeBanner: false));
}

class CementBagCounterApp extends StatefulWidget {
  @override
  State<CementBagCounterApp> createState() => _CementBagCounterAppState();
}

class _CementBagCounterAppState extends State<CementBagCounterApp> {
  File? _image;
  int? _count;
  bool _loading = false;
  String? _error;
  Map? _lastResult;

  final ImagePicker _picker = ImagePicker();

  // Roboflow new workflow endpoint
  final String endpoint =
      'https://serverless.roboflow.com/infer/workflows/demo-sa2bz/detect-count-and-visualize-7';

  final String apiKey = 'Npp49e9rNndYTGP94QGd';

  Future<void> _pickImage(ImageSource source) async {
    final picked = await _picker.pickImage(source: source);
    if (picked != null) {
      setState(() {
        _image = File(picked.path);
        _count = null;
        _error = null;
        _lastResult = null;
        _loading = true;
      });
      await _uploadAndDetect(File(picked.path));
    }
  }

  Future<void> _uploadAndDetect(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      final body = json.encode({
        "api_key": apiKey,
        "inputs": {
          "image": {"type": "base64", "value": base64Image}
        }
      });

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {"Content-Type": "application/json"},
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // Try to count the predictions
        int count = 0;
        // Roboflow workflows usually output detections in a 'predictions' list
        if (data['results'] != null &&
            data['results']['image'] != null &&
            data['results']['image']['predictions'] is List) {
          count = data['results']['image']['predictions'].length;
        }
        setState(() {
          _count = count;
          _lastResult = data;
          _loading = false;
        });
      } else {
        setState(() {
          _loading = false;
          _error = "API Error: ${response.statusCode}\n${response.body}";
        });
      }
    } catch (e) {
      setState(() {
        _loading = false;
        _error = "Error: $e";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Cement Bag Counter'),
      ),
      body: Center(
        child: _loading
            ? CircularProgressIndicator()
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _image != null
                        ? Image.file(_image!, height: 250)
                        : Container(
                            height: 250,
                            color: Colors.grey[300],
                            child: Icon(Icons.photo, size: 120, color: Colors.grey[700]),
                          ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.camera),
                          icon: Icon(Icons.camera_alt),
                          label: Text("Camera"),
                        ),
                        const SizedBox(width: 20),
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.gallery),
                          icon: Icon(Icons.photo_library),
                          label: Text("Gallery"),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    _error != null
                        ? Text(_error!,
                            style: TextStyle(color: Colors.red, fontSize: 16))
                        : _count != null
                            ? Text(
                                "Detected Cement Bags: $_count",
                                style: TextStyle(
                                    fontSize: 26,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green[700]),
                              )
                            : Text(
                                "Select an image to count cement bags.",
                                style: TextStyle(fontSize: 16),
                              ),
                    const SizedBox(height: 20),
                    if (_lastResult != null)
                      ExpansionTile(
                        title: Text("Raw Detection Output (Debug)"),
                        children: [
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Text(
                              JsonEncoder.withIndent('  ').convert(_lastResult),
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
      ),
    );
  }
}
