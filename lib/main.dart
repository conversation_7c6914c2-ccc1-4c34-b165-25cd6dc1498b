import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'dart:ui' as ui;

void main() {
  runApp(MaterialApp(home: CementBagCounterApp(), debugShowCheckedModeBanner: false));
}

class CementBagCounterApp extends StatefulWidget {
  @override
  State<CementBagCounterApp> createState() => _CementBagCounterAppState();
}

class _CementBagCounterAppState extends State<CementBagCounterApp> {
  File? _image;
  int? _count;
  bool _loading = false;
  String? _error;
  Map? _lastResult;
  String? _rawJsonInfo;
  List<Map<String, dynamic>>? _detectionBoxes;
  ui.Image? _imageForPainting;
  String _loadingStatus = "Processing...";

  final ImagePicker _picker = ImagePicker();

  // Roboflow latest workflow endpoint
  final String endpoint =
      'https://serverless.roboflow.com/infer/workflows/demo-sa2bz/detect-count-and-visualize-8';

  final String apiKey = 'Npp49e9rNndYTGP94QGd';

  Future<void> _pickImage(ImageSource source) async {
    final picked = await _picker.pickImage(source: source);
    if (picked != null) {
      // Show image immediately for better UX
      setState(() {
        _image = File(picked.path);
        _loading = true;
      });

      // Clear previous results after showing new image
      setState(() {
        _count = null;
        _error = null;
        _lastResult = null;
        _rawJsonInfo = null;
        _detectionBoxes = null;
        _imageForPainting = null;
      });

      // Start detection process
      await _uploadAndDetect(File(picked.path));
    }
  }

  Future<void> _uploadAndDetect(File imageFile) async {
    setState(() {
      _loading = true;
      _count = null;
      _error = null;
      _lastResult = null;
      _loadingStatus = "Optimizing image...";
    });

    try {
      // Start loading image for painting immediately (parallel processing)
      final imageLoadingFuture = _loadImageForPainting(imageFile);

      // Optimize image size for faster upload (compress if too large)
      final bytes = await _optimizeImageBytes(imageFile);

      setState(() {
        _loadingStatus = "Encoding image...";
      });

      final base64Image = base64Encode(bytes);

      setState(() {
        _loadingStatus = "Detecting cement bags...";
      });

      // Use optimized HTTP client with the latest endpoint
      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          "Accept-Encoding": "gzip, deflate",
          "Connection": "keep-alive",
        },
        body: json.encode({
          "api_key": apiKey,
          "inputs": {
            "image": {"type": "base64", "value": base64Image}
          }
        }),
      ).timeout(Duration(seconds: 15)); // Faster timeout

      if (response.statusCode == 200) {
        setState(() {
          _loadingStatus = "Processing results...";
        });

        final data = json.decode(response.body);
        int count = 0;
        String rawJsonInfo = "";

        // Check for count_objects in raw JSON first (priority)
        List<String> jsonAnalysis = [];

        // Check all possible count_objects locations
        if (data['count_objects'] != null) {
          jsonAnalysis.add("count_objects: ${data['count_objects']}");
          count = _extractCount(data['count_objects']);
        }
        if (data['results'] != null && data['results']['count_objects'] != null) {
          jsonAnalysis.add("results.count_objects: ${data['results']['count_objects']}");
          if (count == 0) count = _extractCount(data['results']['count_objects']);
        }
        if (data['results'] != null && data['results']['image'] != null && data['results']['image']['count_objects'] != null) {
          jsonAnalysis.add("results.image.count_objects: ${data['results']['image']['count_objects']}");
          if (count == 0) count = _extractCount(data['results']['image']['count_objects']);
        }

        // Check for count_objects in outputs array
        if (data['outputs'] is List && (data['outputs'] as List).isNotEmpty) {
          final outputs = data['outputs'] as List;
          for (int i = 0; i < outputs.length; i++) {
            if (outputs[i] is Map && outputs[i]['count_objects'] != null) {
              jsonAnalysis.add("outputs[$i].count_objects: ${outputs[i]['count_objects']}");
              if (count == 0) count = _extractCount(outputs[i]['count_objects']);
            }
          }
        }

        // If no count_objects found, fall back to filtered predictions
        if (count == 0 && data['results'] != null &&
            data['results']['image'] != null &&
            data['results']['image']['predictions'] is List) {

          final predictions = data['results']['image']['predictions'] as List;
          final filteredPredictions = predictions
              .where((d) => d['class'] == 'bags' && (d['confidence'] ?? 0) > 0.5)
              .toList();

          count = filteredPredictions.length;
          jsonAnalysis.add("Filtered 'bags' predictions (confidence > 50%): $count");
        }

        rawJsonInfo = jsonAnalysis.isNotEmpty
            ? "Raw JSON Analysis:\n${jsonAnalysis.join('\n')}\n\nUsing count: $count"
            : "No count fields found in JSON";

        // Extract detection boxes for visualization from the new endpoint
        List<Map<String, dynamic>> detectionBoxes = [];

        if (data['results'] != null &&
            data['results']['image'] != null &&
            data['results']['image']['predictions'] is List) {

          final predictions = data['results']['image']['predictions'] as List;

          for (var prediction in predictions) {
            if (prediction is Map && prediction['class'] == 'bags' && (prediction['confidence'] ?? 0) > 0.5) {
              // Extract bounding box coordinates
              double? x, y, width, height;
              String? className;
              double? confidence;

              // Try different coordinate formats
              if (prediction['x'] != null && prediction['y'] != null &&
                  prediction['width'] != null && prediction['height'] != null) {
                x = (prediction['x'] as num).toDouble();
                y = (prediction['y'] as num).toDouble();
                width = (prediction['width'] as num).toDouble();
                height = (prediction['height'] as num).toDouble();
              } else if (prediction['bbox'] is List && (prediction['bbox'] as List).length >= 4) {
                final bbox = prediction['bbox'] as List;
                x = (bbox[0] as num).toDouble();
                y = (bbox[1] as num).toDouble();
                width = (bbox[2] as num).toDouble();
                height = (bbox[3] as num).toDouble();
              }

              className = prediction['class'] ?? 'bags';
              confidence = (prediction['confidence'] ?? 0.0) as double;

              if (x != null && y != null && width != null && height != null) {
                detectionBoxes.add({
                  'x': x,
                  'y': y,
                  'width': width,
                  'height': height,
                  'class': className,
                  'confidence': confidence,
                });
              }
            }
          }
        }

        // Wait for image loading (started earlier in parallel)
        await imageLoadingFuture;

        setState(() {
          _count = count;
          _lastResult = data;
          _rawJsonInfo = rawJsonInfo;
          _detectionBoxes = detectionBoxes;
          _loading = false;
        });
      } else {
        setState(() {
          _loading = false;
          _error = "API Error: ${response.statusCode}\n${response.body}";
          _rawJsonInfo = null;
        });
      }
    } catch (e) {
      setState(() {
        _loading = false;
        _error = "Error: $e";
      });
    }
  }

  int _extractCount(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    if (value is List) return value.length;
    return 0;
  }

  Future<Uint8List> _optimizeImageBytes(File imageFile) async {
    final bytes = await imageFile.readAsBytes();

    // If image is larger than 1MB, compress it for faster upload
    if (bytes.length > 1024 * 1024) {
      try {
        final codec = await ui.instantiateImageCodec(
          bytes,
          targetWidth: 1024, // Resize to max 1024px width for faster processing
          targetHeight: 1024,
        );
        final frame = await codec.getNextFrame();
        final byteData = await frame.image.toByteData(format: ui.ImageByteFormat.png);
        return byteData!.buffer.asUint8List();
      } catch (e) {
        // If compression fails, use original
        return bytes;
      }
    }
    return bytes;
  }

  Future<void> _loadImageForPainting(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    setState(() {
      _imageForPainting = frame.image;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          '🏗️ Cement Bag Counter',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 22,
          ),
        ),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: _loading
          ? Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.green[50]!, Colors.white],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(30),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.1),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.green[400]!, Colors.green[600]!],
                              ),
                              borderRadius: BorderRadius.circular(40),
                            ),
                            child: Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 4,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                          ),
                          SizedBox(height: 25),
                          Text(
                            _loadingStatus,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.green[700],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 10),
                          Text(
                            "⚡ AI-Powered Detection",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.green[50]!, Colors.white],
                ),
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Hero Image Card
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 15,
                            spreadRadius: 2,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: _image != null
                            ? Container(
                                height: 320,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [Colors.green[100]!, Colors.blue[50]!],
                                  ),
                                ),
                                child: _imageForPainting != null && _detectionBoxes != null && _detectionBoxes!.isNotEmpty
                                    ? AspectRatio(
                                        aspectRatio: _imageForPainting!.width / _imageForPainting!.height,
                                        child: CustomPaint(
                                          painter: DetectionBoxPainter(_imageForPainting!, _detectionBoxes!),
                                          size: Size.infinite,
                                        ),
                                      )
                                    : Image.file(_image!, fit: BoxFit.cover),
                              )
                            : Container(
                                height: 320,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [Colors.grey[100]!, Colors.grey[50]!],
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(20),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(50),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0.2),
                                            blurRadius: 10,
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        Icons.photo_camera_outlined,
                                        size: 60,
                                        color: Colors.green[600],
                                      ),
                                    ),
                                    SizedBox(height: 20),
                                    Text(
                                      "📸 Select or capture an image",
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      "AI will detect and count cement bags",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 30),
                    // Action Buttons Section
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            spreadRadius: 1,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Text(
                            "📱 Choose Detection Method",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                          SizedBox(height: 20),
                          Row(
                            children: [
                              Expanded(
                                child: _buildActionButton(
                                  icon: Icons.camera_alt,
                                  label: "Camera",
                                  subtitle: "Take Photo",
                                  color: Colors.blue,
                                  onPressed: () => _pickImage(ImageSource.camera),
                                ),
                              ),
                              SizedBox(width: 15),
                              Expanded(
                                child: _buildActionButton(
                                  icon: Icons.photo_library,
                                  label: "Gallery",
                                  subtitle: "Select Image",
                                  color: Colors.purple,
                                  onPressed: () => _pickImage(ImageSource.gallery),
                                ),
                              ),
                            ],
                          ),

                        ],
                      ),
                    ),
                    const SizedBox(height: 30),
                    // Results Section
                    _error != null
                        ? Container(
                            padding: EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.red[50],
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(color: Colors.red[200]!),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.error_outline, color: Colors.red[600], size: 24),
                                SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _error!,
                                    style: TextStyle(
                                      color: Colors.red[700],
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : _count != null
                            ? Container(
                                padding: EdgeInsets.all(25),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [Colors.green[50]!, Colors.green[100]!],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.green.withOpacity(0.2),
                                      blurRadius: 15,
                                      spreadRadius: 2,
                                      offset: Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  children: [
                                    // Main Count Display
                                    Container(
                                      padding: EdgeInsets.all(20),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(15),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.05),
                                            blurRadius: 10,
                                            spreadRadius: 1,
                                          ),
                                        ],
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            padding: EdgeInsets.all(15),
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: [Colors.green[400]!, Colors.green[600]!],
                                              ),
                                              borderRadius: BorderRadius.circular(50),
                                            ),
                                            child: Icon(
                                              Icons.inventory_2,
                                              color: Colors.white,
                                              size: 30,
                                            ),
                                          ),
                                          SizedBox(width: 20),
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "Detected Bags",
                                                style: TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.grey[600],
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              Text(
                                                "$_count",
                                                style: TextStyle(
                                                  fontSize: 36,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.green[700],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (_rawJsonInfo != null) ...[
                                      const SizedBox(height: 20),
                                      Container(
                                        padding: EdgeInsets.all(18),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(15),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.05),
                                              blurRadius: 10,
                                              spreadRadius: 1,
                                            ),
                                          ],
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(Icons.analytics, color: Colors.blue[600], size: 20),
                                                SizedBox(width: 8),
                                                Text(
                                                  "📊 Detection Analysis",
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.blue[800],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 12),
                                            Container(
                                              padding: EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color: Colors.blue[50],
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                              child: Text(
                                                _rawJsonInfo!,
                                                style: TextStyle(
                                                  fontSize: 13,
                                                  color: Colors.blue[700],
                                                  fontFamily: 'monospace',
                                                  height: 1.4,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              )
                            : Container(
                                padding: EdgeInsets.all(30),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 10,
                                      spreadRadius: 1,
                                      offset: Offset(0, 3),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.touch_app,
                                      size: 50,
                                      color: Colors.grey[400],
                                    ),
                                    SizedBox(height: 15),
                                    Text(
                                      "Ready to Count!",
                                      style: TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      "Select an image to detect and count cement bags using AI",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[500],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                    const SizedBox(height: 20),
                    if (_lastResult != null)
                      ExpansionTile(
                        title: Text("Detection Details (Debug)"),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Show response structure analysis
                                Text(
                                  "Response Structure Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                                Text("Keys in response: ${_lastResult!.keys.toList()}"),
                                if (_lastResult!['results'] != null)
                                  Text("Keys in results: ${(_lastResult!['results'] as Map).keys.toList()}"),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null)
                                  Text("Keys in results.image: ${(_lastResult!['results']['image'] as Map).keys.toList()}"),

                                // Show count_objects specifically
                                const SizedBox(height: 10),
                                Text(
                                  "Count Objects Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.blue),
                                ),
                                if (_lastResult!['count_objects'] != null)
                                  Text("count_objects: ${_lastResult!['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                if (_lastResult!['results'] != null && _lastResult!['results']['count_objects'] != null)
                                  Text("results.count_objects: ${_lastResult!['results']['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null && _lastResult!['results']['image']['count_objects'] != null)
                                  Text("results.image.count_objects: ${_lastResult!['results']['image']['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                const SizedBox(height: 10),

                                // Try to find and display predictions from any location
                                Builder(builder: (context) {
                                  List<dynamic>? predictions;
                                  String location = "";

                                  if (_lastResult!['results'] != null &&
                                      _lastResult!['results']['image'] != null &&
                                      _lastResult!['results']['image']['predictions'] is List) {
                                    predictions = _lastResult!['results']['image']['predictions'] as List;
                                    location = "results.image.predictions";
                                  } else if (_lastResult!['predictions'] is List) {
                                    predictions = _lastResult!['predictions'] as List;
                                    location = "predictions";
                                  } else if (_lastResult!['results'] != null && _lastResult!['results']['predictions'] is List) {
                                    predictions = _lastResult!['results']['predictions'] as List;
                                    location = "results.predictions";
                                  } else if (_lastResult!['outputs'] is List) {
                                    predictions = _lastResult!['outputs'] as List;
                                    location = "outputs";
                                  }

                                  if (predictions != null && predictions.isNotEmpty) {
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Found ${predictions.length} predictions in: $location",
                                             style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
                                        const SizedBox(height: 5),
                                        ...predictions.take(10).toList().asMap().entries.map((entry) {
                                          final index = entry.key;
                                          final prediction = entry.value;
                                          final confidence = (prediction['confidence'] ?? prediction['score'] ?? 0.0);
                                          final confidencePercent = (confidence is double && confidence <= 1.0)
                                              ? confidence * 100
                                              : confidence.toDouble();
                                          final className = prediction['class'] ?? prediction['name'] ?? prediction['label'] ?? 'unknown';
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 1.0),
                                            child: Text(
                                              "Detection ${index + 1}: $className (${confidencePercent.toStringAsFixed(1)}%)",
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: confidencePercent > 50 ? Colors.green : Colors.orange,
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                        if (predictions.length > 10)
                                          Text("... and ${predictions.length - 10} more detections"),
                                      ],
                                    );
                                  } else {
                                    return Text("No predictions found in expected locations",
                                               style: TextStyle(color: Colors.red));
                                  }
                                }),

                                const SizedBox(height: 10),
                                ExpansionTile(
                                  title: Text("Raw JSON Response"),
                                  children: [
                                    SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Text(
                                        JsonEncoder.withIndent('  ').convert(_lastResult),
                                        style: TextStyle(fontSize: 10),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required String subtitle,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withOpacity(0.8), color],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            spreadRadius: 1,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: onPressed,
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 24,
                ),
                SizedBox(height: 6),
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DetectionBoxPainter extends CustomPainter {
  final ui.Image image;
  final List<Map<String, dynamic>> detectionBoxes;

  DetectionBoxPainter(this.image, this.detectionBoxes);

  @override
  void paint(Canvas canvas, Size size) {
    // Draw the image
    final paint = Paint();
    final src = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final dst = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawImageRect(image, src, dst, paint);

    // Calculate scaling factors
    final scaleX = size.width / image.width;
    final scaleY = size.height / image.height;

    // Draw detection boxes
    final boxPaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    for (var box in detectionBoxes) {
      final x = (box['x'] as double) * scaleX;
      final y = (box['y'] as double) * scaleY;
      final width = (box['width'] as double) * scaleX;
      final height = (box['height'] as double) * scaleY;

      // Draw bounding box
      final rect = Rect.fromLTWH(x, y, width, height);
      canvas.drawRect(rect, boxPaint);

      // Draw label exactly like in the reference image
      final className = box['class'] ?? 'bags';
      final confidence = ((box['confidence'] ?? 0.0) * 100).toStringAsFixed(0);
      final label = '$className $confidence%';

      textPainter.text = TextSpan(
        text: label,
        style: TextStyle(
          color: Colors.black,
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();

      // Draw yellow/green label background exactly like reference
      final labelPadding = 4.0;
      final labelRect = Rect.fromLTWH(
        x,
        y - textPainter.height - labelPadding * 2,
        textPainter.width + labelPadding * 2,
        textPainter.height + labelPadding * 2
      );

      // Use yellow-green color like in reference image
      canvas.drawRect(labelRect, Paint()..color = Color(0xFFCCFF00));

      // Draw label text
      textPainter.paint(canvas, Offset(x + labelPadding, y - textPainter.height - labelPadding));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
