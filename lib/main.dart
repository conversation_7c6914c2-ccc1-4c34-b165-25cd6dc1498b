import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'dart:ui' as ui;

void main() {
  runApp(MaterialApp(home: CementBagCounterApp(), debugShowCheckedModeBanner: false));
}

class CementBagCounterApp extends StatefulWidget {
  @override
  State<CementBagCounterApp> createState() => _CementBagCounterAppState();
}

class _CementBagCounterAppState extends State<CementBagCounterApp> {
  File? _image;
  int? _count;
  bool _loading = false;
  String? _error;
  Map? _lastResult;
  String? _rawJsonInfo;

  final ImagePicker _picker = ImagePicker();

  // Roboflow new workflow endpoint
  final String endpoint =
      'https://serverless.roboflow.com/infer/workflows/demo-sa2bz/detect-count-and-visualize-7';

  final String apiKey = 'Npp49e9rNndYTGP94QGd';

  Future<void> _pickImage(ImageSource source) async {
    final picked = await _picker.pickImage(source: source);
    if (picked != null) {
      setState(() {
        _image = File(picked.path);
        _count = null;
        _error = null;
        _lastResult = null;
        _rawJsonInfo = null;
        _loading = true;
      });
      await _uploadAndDetect(File(picked.path));
    }
  }

  Future<void> _uploadAndDetect(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      final body = json.encode({
        "api_key": apiKey,
        "inputs": {
          "image": {"type": "base64", "value": base64Image}
        }
      });

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {"Content-Type": "application/json"},
        body: body,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        int count = 0;
        String debugInfo = "";
        String rawJsonInfo = "";

        // Extract comprehensive information from raw JSON
        List<String> jsonAnalysis = [];

        // Check all possible count fields
        if (data['count_objects'] != null) {
          jsonAnalysis.add("count_objects: ${data['count_objects']}");
          count = _extractCount(data['count_objects']);
        }
        if (data['results'] != null && data['results']['count_objects'] != null) {
          jsonAnalysis.add("results.count_objects: ${data['results']['count_objects']}");
          if (count == 0) count = _extractCount(data['results']['count_objects']);
        }
        if (data['results'] != null && data['results']['image'] != null && data['results']['image']['count_objects'] != null) {
          jsonAnalysis.add("results.image.count_objects: ${data['results']['image']['count_objects']}");
          if (count == 0) count = _extractCount(data['results']['image']['count_objects']);
        }

        // Check for predictions arrays
        if (data['predictions'] is List) {
          jsonAnalysis.add("predictions.length: ${(data['predictions'] as List).length}");
          if (count == 0) count = (data['predictions'] as List).length;
        }
        if (data['results'] != null && data['results']['predictions'] is List) {
          jsonAnalysis.add("results.predictions.length: ${(data['results']['predictions'] as List).length}");
          if (count == 0) count = (data['results']['predictions'] as List).length;
        }
        if (data['results'] != null && data['results']['image'] != null && data['results']['image']['predictions'] is List) {
          jsonAnalysis.add("results.image.predictions.length: ${(data['results']['image']['predictions'] as List).length}");
          if (count == 0) count = (data['results']['image']['predictions'] as List).length;
        }

        // Check for count_objects in outputs array
        if (data['outputs'] is List && (data['outputs'] as List).isNotEmpty) {
          final outputs = data['outputs'] as List;
          for (int i = 0; i < outputs.length; i++) {
            if (outputs[i] is Map && outputs[i]['count_objects'] != null) {
              jsonAnalysis.add("outputs[$i].count_objects: ${outputs[i]['count_objects']}");
              if (count == 0) count = _extractCount(outputs[i]['count_objects']);
            }
          }
        }

        // Check for other possible count fields
        if (data['total_objects'] != null) {
          jsonAnalysis.add("total_objects: ${data['total_objects']}");
          if (count == 0) count = _extractCount(data['total_objects']);
        }
        if (data['object_count'] != null) {
          jsonAnalysis.add("object_count: ${data['object_count']}");
          if (count == 0) count = _extractCount(data['object_count']);
        }
        if (data['detected_objects'] != null) {
          jsonAnalysis.add("detected_objects: ${data['detected_objects']}");
          if (count == 0) count = _extractCount(data['detected_objects']);
        }

        rawJsonInfo = jsonAnalysis.isNotEmpty
            ? "Raw JSON Analysis:\n${jsonAnalysis.join('\n')}\n\nUsing count: $count"
            : "No count fields found in JSON";

        debugInfo = "Extracted count: $count from ${jsonAnalysis.isNotEmpty ? jsonAnalysis.first : 'unknown source'}";

        setState(() {
          _count = count;
          _lastResult = data;
          _rawJsonInfo = rawJsonInfo;
          _loading = false;
          _error = null; // Clear error since we have results
        });
      } else {
        setState(() {
          _loading = false;
          _error = "API Error: ${response.statusCode}\n${response.body}";
          _rawJsonInfo = null;
        });
      }
    } catch (e) {
      setState(() {
        _loading = false;
        _error = "Error: $e";
      });
    }
  }

  int _extractCount(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    if (value is List) return value.length;
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Cement Bag Counter (Workflow API)'),
      ),
      body: Center(
        child: _loading
            ? CircularProgressIndicator()
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _image != null
                        ? Image.file(_image!, height: 250)
                        : Container(
                            height: 250,
                            color: Colors.grey[300],
                            child: Icon(Icons.photo, size: 120, color: Colors.grey[700]),
                          ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.camera),
                          icon: Icon(Icons.camera_alt),
                          label: Text("Camera"),
                        ),
                        const SizedBox(width: 20),
                        ElevatedButton.icon(
                          onPressed: () => _pickImage(ImageSource.gallery),
                          icon: Icon(Icons.photo_library),
                          label: Text("Gallery"),
                        ),
                      ],
                    ),
                    const SizedBox(height: 30),
                    _error != null
                        ? Text(_error!,
                            style: TextStyle(color: Colors.red, fontSize: 16))
                        : _count != null
                            ? Column(
                                children: [
                                  Text(
                                    "Detected Cement Bags: $_count",
                                    style: TextStyle(
                                        fontSize: 26,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green[700]),
                                  ),
                                  if (_rawJsonInfo != null) ...[
                                    const SizedBox(height: 15),
                                    Container(
                                      padding: EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.blue[50],
                                        border: Border.all(color: Colors.blue[200]!),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "📊 Raw JSON Object Count",
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.blue[800],
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            _rawJsonInfo!,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.blue[700],
                                              fontFamily: 'monospace',
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              )
                            : Text(
                                "Select an image to count cement bags.",
                                style: TextStyle(fontSize: 16),
                              ),
                    const SizedBox(height: 20),
                    if (_lastResult != null)
                      ExpansionTile(
                        title: Text("Detection Details (Debug)"),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Show response structure analysis
                                Text(
                                  "Response Structure Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                                Text("Keys in response: ${_lastResult!.keys.toList()}"),
                                if (_lastResult!['results'] != null)
                                  Text("Keys in results: ${(_lastResult!['results'] as Map).keys.toList()}"),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null)
                                  Text("Keys in results.image: ${(_lastResult!['results']['image'] as Map).keys.toList()}"),

                                // Show count_objects specifically
                                const SizedBox(height: 10),
                                Text(
                                  "Count Objects Analysis:",
                                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.blue),
                                ),
                                if (_lastResult!['count_objects'] != null)
                                  Text("count_objects: ${_lastResult!['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                if (_lastResult!['results'] != null && _lastResult!['results']['count_objects'] != null)
                                  Text("results.count_objects: ${_lastResult!['results']['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                if (_lastResult!['results'] != null && _lastResult!['results']['image'] != null && _lastResult!['results']['image']['count_objects'] != null)
                                  Text("results.image.count_objects: ${_lastResult!['results']['image']['count_objects']}",
                                       style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                                const SizedBox(height: 10),

                                // Try to find and display predictions from any location
                                Builder(builder: (context) {
                                  List<dynamic>? predictions;
                                  String location = "";

                                  if (_lastResult!['results'] != null &&
                                      _lastResult!['results']['image'] != null &&
                                      _lastResult!['results']['image']['predictions'] is List) {
                                    predictions = _lastResult!['results']['image']['predictions'] as List;
                                    location = "results.image.predictions";
                                  } else if (_lastResult!['predictions'] is List) {
                                    predictions = _lastResult!['predictions'] as List;
                                    location = "predictions";
                                  } else if (_lastResult!['results'] != null && _lastResult!['results']['predictions'] is List) {
                                    predictions = _lastResult!['results']['predictions'] as List;
                                    location = "results.predictions";
                                  } else if (_lastResult!['outputs'] is List) {
                                    predictions = _lastResult!['outputs'] as List;
                                    location = "outputs";
                                  }

                                  if (predictions != null && predictions.isNotEmpty) {
                                    return Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Found ${predictions.length} predictions in: $location",
                                             style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
                                        const SizedBox(height: 5),
                                        ...predictions.take(10).toList().asMap().entries.map((entry) {
                                          final index = entry.key;
                                          final prediction = entry.value;
                                          final confidence = (prediction['confidence'] ?? prediction['score'] ?? 0.0);
                                          final confidencePercent = (confidence is double && confidence <= 1.0)
                                              ? confidence * 100
                                              : confidence.toDouble();
                                          final className = prediction['class'] ?? prediction['name'] ?? prediction['label'] ?? 'unknown';
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 1.0),
                                            child: Text(
                                              "Detection ${index + 1}: $className (${confidencePercent.toStringAsFixed(1)}%)",
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: confidencePercent > 50 ? Colors.green : Colors.orange,
                                              ),
                                            ),
                                          );
                                        }).toList(),
                                        if (predictions.length > 10)
                                          Text("... and ${predictions.length - 10} more detections"),
                                      ],
                                    );
                                  } else {
                                    return Text("No predictions found in expected locations",
                                               style: TextStyle(color: Colors.red));
                                  }
                                }),

                                const SizedBox(height: 10),
                                ExpansionTile(
                                  title: Text("Raw JSON Response"),
                                  children: [
                                    SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Text(
                                        JsonEncoder.withIndent('  ').convert(_lastResult),
                                        style: TextStyle(fontSize: 10),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
      ),
    );
  }
}
