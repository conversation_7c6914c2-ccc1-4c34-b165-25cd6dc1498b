import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:geolocator/geolocator.dart';
import 'package:learning2/StaffScreen/staff_home_screen.dart';
import 'package:learning2/WorkerScreen/Worker_Home_Screen.dart';
import 'package:learning2/screens/Home_screen.dart';
import 'package:learning2/screens/firebase_api.dart';
import 'package:learning2/screens/notification_screen.dart';
import 'package:learning2/screens/splash_screen.dart';
import 'package:provider/provider.dart';
import 'package:learning2/services/background_service.dart';
import 'package:learning2/services/display_service.dart';
import 'package:learning2/widgets/performance_monitor.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:learning2/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Performance optimizations for high refresh rate
  await _configurePerformanceSettings();

  // Initialize display service for high refresh rate
  await DisplayService.instance.initialize();

  await Firebase.initializeApp();

  // Create the notification provider
  final notificationProvider = NotificationProvider();

  // Initialize Firebase API with the notification provider
  await FirebaseApi().initNotification(notificationProvider);

  // Initialize background location service
  // final backgroundService = BackgroundLocationService();

  // Initialize the service but don't auto-start it
  // This ensures the user must manually start the service
  // await backgroundService.initializeService(autoStart: false);

  print('Background service initialized but not started - user must start it manually');

  runApp(
    ChangeNotifierProvider.value(
      value: notificationProvider,
      child: const MyApp(),
    ),
  );
}

/// Performance configuration function for high refresh rate support
Future<void> _configurePerformanceSettings() async {
  // Enable high refresh rate for supported devices
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Set preferred orientations for optimal performance
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Configure system UI overlay style for performance
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  // Enable hardware acceleration and high refresh rate
  if (SchedulerBinding.instance.lifecycleState != null) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // Force high refresh rate if available
      SchedulerBinding.instance.scheduleWarmUpFrame();
    });
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'SPARSH',
      theme: AppTheme.lightTheme,
      home: const PerformanceMonitor(
        showOverlay: false, // Set to true for debugging
        enableLogging: true,
        child: HomeScreen(),
      ),
      // Performance optimizations
      builder: (context, child) {
        return MediaQuery(
          // Disable text scaling for consistent performance
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
      // Enable high refresh rate scrolling
      scrollBehavior: const MaterialScrollBehavior().copyWith(
        physics: const BouncingScrollPhysics(),
        scrollbars: false,
      ),
    );
  }
}
