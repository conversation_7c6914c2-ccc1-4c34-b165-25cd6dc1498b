import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class LiveCameraScreen extends StatefulWidget {
  final List<CameraDescription> cameras;
  
  const LiveCameraScreen({Key? key, required this.cameras}) : super(key: key);

  @override
  State<LiveCameraScreen> createState() => _LiveCameraScreenState();
}

class _LiveCameraScreenState extends State<LiveCameraScreen> {
  late CameraController _cameraController;
  bool _isDetecting = false;
  bool _isInitialized = false;
  int _detectedBags = 0;
  String? _error;
  Timer? _timer;
  String _status = "Initializing camera...";

  // Roboflow workflow endpoint
  final String endpoint =
      'https://serverless.roboflow.com/infer/workflows/demo-sa2bz/detect-count-and-visualize-8';
  final String apiKey = 'Npp49e9rNndYTGP94QGd';

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      _cameraController = CameraController(
        widget.cameras.first,
        ResolutionPreset.medium,
        enableAudio: false,
      );
      
      await _cameraController.initialize();
      
      setState(() {
        _isInitialized = true;
        _status = "Camera ready - Starting detection...";
      });
      
      // Start periodic detection every 3 seconds
      _timer = Timer.periodic(Duration(seconds: 3), (timer) => _detectBags());
      
    } catch (e) {
      setState(() {
        _error = "Camera initialization failed: $e";
      });
    }
  }

  @override
  void dispose() {
    _cameraController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _detectBags() async {
    if (!_cameraController.value.isInitialized || _isDetecting) return;
    
    setState(() {
      _isDetecting = true;
      _status = "Detecting cement bags...";
    });

    try {
      // Take picture (saves to temp file)
      final XFile file = await _cameraController.takePicture();
      final bytes = await file.readAsBytes();
      final base64Image = base64Encode(bytes);

      setState(() {
        _status = "Analyzing with AI...";
      });

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          "Accept-Encoding": "gzip, deflate",
          "Connection": "keep-alive",
        },
        body: json.encode({
          "api_key": apiKey,
          "inputs": {
            "image": {"type": "base64", "value": base64Image}
          }
        }),
      ).timeout(Duration(seconds: 15));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        int count = 0;
        
        // Check for count_objects in raw JSON first (priority)
        if (data['count_objects'] != null) {
          count = _extractCount(data['count_objects']);
        } else if (data['results'] != null && data['results']['count_objects'] != null) {
          count = _extractCount(data['results']['count_objects']);
        } else if (data['results'] != null && data['results']['image'] != null && data['results']['image']['count_objects'] != null) {
          count = _extractCount(data['results']['image']['count_objects']);
        } else if (data['outputs'] is List && (data['outputs'] as List).isNotEmpty) {
          final outputs = data['outputs'] as List;
          for (var output in outputs) {
            if (output is Map && output['count_objects'] != null) {
              count = _extractCount(output['count_objects']);
              break;
            }
          }
        } else if (data['results'] != null &&
            data['results']['image'] != null &&
            data['results']['image']['predictions'] is List) {
          // Fallback to filtered predictions
          final predictions = data['results']['image']['predictions'] as List;
          count = predictions
              .where((d) => d['class'] == 'bags' && (d['confidence'] ?? 0) > 0.5)
              .length;
        }
        
        setState(() {
          _detectedBags = count;
          _error = null;
          _status = "Live detection active";
        });
      } else {
        setState(() {
          _error = "API Error: ${response.statusCode}";
          _status = "Detection failed";
        });
      }
    } catch (e) {
      setState(() {
        _error = "Error: $e";
        _status = "Detection failed";
      });
    }
    
    setState(() {
      _isDetecting = false;
    });
  }

  int _extractCount(dynamic value) {
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    if (value is List) return value.length;
    return 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Live Cement Bag Counter'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_timer?.isActive == true ? Icons.pause : Icons.play_arrow),
            onPressed: () {
              if (_timer?.isActive == true) {
                _timer?.cancel();
                setState(() {
                  _status = "Detection paused";
                });
              } else {
                _timer = Timer.periodic(Duration(seconds: 3), (timer) => _detectBags());
                setState(() {
                  _status = "Live detection active";
                });
              }
            },
          ),
        ],
      ),
      body: !_isInitialized
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                  SizedBox(height: 20),
                  Text(
                    _error ?? _status,
                    style: TextStyle(
                      fontSize: 16,
                      color: _error != null ? Colors.red : Colors.green[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          : Stack(
              children: [
                // Camera Preview
                CameraPreview(_cameraController),
                
                // Detection Count Overlay
                Positioned(
                  top: 20,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                      decoration: BoxDecoration(
                        color: Colors.black87,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.green, width: 2),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'DETECTED BAGS',
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1.2,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '$_detectedBags',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // Status Overlay
                Positioned(
                  bottom: 100,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_isDetecting)
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                              ),
                            ),
                          if (_isDetecting) SizedBox(width: 8),
                          Text(
                            _error ?? _status,
                            style: TextStyle(
                              color: _error != null ? Colors.red : Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // Detection Info
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '🎯 Live detection every 3 seconds\n⚡ Optimized for speed\n📊 Using latest Roboflow API',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
