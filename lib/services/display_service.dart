import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

/// Service for managing display settings and refresh rate
class DisplayService {
  static const MethodChannel _channel = MethodChannel('com.example.learning2/display');
  
  static DisplayService? _instance;
  static DisplayService get instance => _instance ??= DisplayService._();
  
  DisplayService._();
  
  double? _currentRefreshRate;
  bool _highRefreshRateEnabled = false;
  
  /// Get the current display refresh rate
  Future<double> getRefreshRate() async {
    try {
      final double refreshRate = await _channel.invokeMethod('getRefreshRate');
      _currentRefreshRate = refreshRate;
      return refreshRate;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting refresh rate: ${e.message}');
      }
      return 60.0; // Default fallback
    }
  }
  
  /// Enable high refresh rate if supported by the device
  Future<bool> enableHighRefreshRate() async {
    try {
      final bool success = await _channel.invokeMethod('enableHighRefreshRate');
      _highRefreshRateEnabled = success;
      
      if (success) {
        // Update the current refresh rate after enabling
        await getRefreshRate();
        print('High refresh rate enabled successfully. Current rate: ${_currentRefreshRate}Hz');
      }
      
      return success;
    } on PlatformException catch (e) {
      print('Error enabling high refresh rate: ${e.message}');
      return false;
    }
  }
  
  /// Check if high refresh rate is currently enabled
  bool get isHighRefreshRateEnabled => _highRefreshRateEnabled;
  
  /// Get the cached refresh rate (null if not fetched yet)
  double? get currentRefreshRate => _currentRefreshRate;
  
  /// Check if the device supports high refresh rate (>60Hz)
  Future<bool> supportsHighRefreshRate() async {
    final rate = await getRefreshRate();
    return rate > 60.0;
  }
  
  /// Get refresh rate category for optimization purposes
  RefreshRateCategory getRefreshRateCategory() {
    if (_currentRefreshRate == null) return RefreshRateCategory.unknown;
    
    if (_currentRefreshRate! <= 60) {
      return RefreshRateCategory.standard;
    } else if (_currentRefreshRate! <= 90) {
      return RefreshRateCategory.high;
    } else if (_currentRefreshRate! <= 120) {
      return RefreshRateCategory.veryHigh;
    } else {
      return RefreshRateCategory.ultra;
    }
  }
  
  /// Get optimal animation duration based on refresh rate
  Duration getOptimalAnimationDuration({Duration defaultDuration = const Duration(milliseconds: 300)}) {
    final category = getRefreshRateCategory();
    
    switch (category) {
      case RefreshRateCategory.ultra:
        return Duration(milliseconds: (defaultDuration.inMilliseconds * 0.7).round());
      case RefreshRateCategory.veryHigh:
        return Duration(milliseconds: (defaultDuration.inMilliseconds * 0.8).round());
      case RefreshRateCategory.high:
        return Duration(milliseconds: (defaultDuration.inMilliseconds * 0.9).round());
      case RefreshRateCategory.standard:
      case RefreshRateCategory.unknown:
      default:
        return defaultDuration;
    }
  }
  
  /// Get optimal frame interval for animations
  Duration getOptimalFrameInterval() {
    if (_currentRefreshRate == null || _currentRefreshRate! <= 60) {
      return const Duration(milliseconds: 16); // ~60 FPS
    } else if (_currentRefreshRate! <= 90) {
      return const Duration(milliseconds: 11); // ~90 FPS
    } else if (_currentRefreshRate! <= 120) {
      return const Duration(milliseconds: 8); // ~120 FPS
    } else {
      return const Duration(milliseconds: 7); // ~144 FPS
    }
  }
  
  /// Initialize the display service and enable high refresh rate
  Future<void> initialize() async {
    try {
      await getRefreshRate();
      await enableHighRefreshRate();
      
      print('Display service initialized:');
      print('- Refresh rate: ${_currentRefreshRate}Hz');
      print('- High refresh rate enabled: $_highRefreshRateEnabled');
      print('- Category: ${getRefreshRateCategory()}');
    } catch (e) {
      print('Error initializing display service: $e');
    }
  }
  
  /// Get performance recommendations based on refresh rate
  PerformanceRecommendations getPerformanceRecommendations() {
    final category = getRefreshRateCategory();
    
    switch (category) {
      case RefreshRateCategory.ultra:
        return PerformanceRecommendations(
          useRepaintBoundary: true,
          reduceAnimationComplexity: false,
          optimizeScrollPhysics: true,
          enableCaching: true,
          maxShadowBlurRadius: 12.0,
          recommendedAnimationCurve: 'easeInOutCubic',
        );
      case RefreshRateCategory.veryHigh:
        return PerformanceRecommendations(
          useRepaintBoundary: true,
          reduceAnimationComplexity: false,
          optimizeScrollPhysics: true,
          enableCaching: true,
          maxShadowBlurRadius: 10.0,
          recommendedAnimationCurve: 'easeInOut',
        );
      case RefreshRateCategory.high:
        return PerformanceRecommendations(
          useRepaintBoundary: true,
          reduceAnimationComplexity: false,
          optimizeScrollPhysics: true,
          enableCaching: false,
          maxShadowBlurRadius: 8.0,
          recommendedAnimationCurve: 'easeInOut',
        );
      case RefreshRateCategory.standard:
      case RefreshRateCategory.unknown:
      default:
        return PerformanceRecommendations(
          useRepaintBoundary: false,
          reduceAnimationComplexity: true,
          optimizeScrollPhysics: false,
          enableCaching: false,
          maxShadowBlurRadius: 6.0,
          recommendedAnimationCurve: 'ease',
        );
    }
  }
}

/// Enum for refresh rate categories
enum RefreshRateCategory {
  unknown,
  standard, // 60Hz
  high,     // 90Hz
  veryHigh, // 120Hz
  ultra,    // 144Hz+
}

/// Performance recommendations based on refresh rate
class PerformanceRecommendations {
  final bool useRepaintBoundary;
  final bool reduceAnimationComplexity;
  final bool optimizeScrollPhysics;
  final bool enableCaching;
  final double maxShadowBlurRadius;
  final String recommendedAnimationCurve;
  
  const PerformanceRecommendations({
    required this.useRepaintBoundary,
    required this.reduceAnimationComplexity,
    required this.optimizeScrollPhysics,
    required this.enableCaching,
    required this.maxShadowBlurRadius,
    required this.recommendedAnimationCurve,
  });
}
